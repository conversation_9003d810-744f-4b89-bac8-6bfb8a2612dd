import request from "@/assets/js/request";
import util from '@/assets/js/public';

export function login(params){      
    let url=util.toUrl(`${process.env.VUE_APP_URL}/oauth/token`,params || {});
    // let url=util.toUrl(`${process.env.VUE_APP_URL}/restLogin`,params || {});
    return request({
        url:url,
        // data:params,
        contentType:'application/json;charset=UTF-8',
        catch:true
    })
}
export function loginFast(){    
    return request({
        url:`${process.env.VUE_APP_URL}/api/restLogin?appcode=${process.env.VUE_APP_APPCODE}`,
        contentType:'application/json;charset=UTF-8'
    })
}
export function sendMes(phone){
    return request({
        url:`${process.env.VUE_APP_URL}/action/logic/sendMsg/anonymous?phone=`+phone+`&appcode=${process.env.VUE_APP_APPCODE}`,
        contentType:'application/json;charset=UTF-8',
        message:false
    });
}
export function getPhone(token){
    return request({
        url:`${process.env.VUE_APP_URL}/action/logic/getPhone/anonymous?token=`+token+`&appcode=${process.env.VUE_APP_APPCODE}`,
        contentType:'application/json;charset=UTF-8',
        catch:true
    });
}
export function oauthToken(params){
    console.log(params)
    let url=util.toUrl(`${process.env.VUE_APP_URL}/oauth/token`,params || {});
    console.log(url)
    return request({
        url:url,
        contentType:'application/json;charset=UTF-8'
    });
}
export function getInfo(){
    return request({
        url:`${process.env.VUE_APP_URL}/getCurrentUser/api`,///getCurrentUser
        contentType:'application/json;charset=UTF-8'
    })
}
export function getInfoSSO(params){
    return request({
        url: util.toUrl(`/${process.env.VUE_APP_APPCODE}/getCurrentUser/sso`,params),///getCurrentUser
        contentType:'application/json;charset=UTF-8'
    })
}
export function captcha(){
    return request({
        url:'/captcha',
        method:'get',
        responseType: 'blob',
        contentType:'application/json;charset=UTF-8'
    })
}
export function logout(){
    return request({
        url:`${process.env.VUE_APP_URL}/restuumslogout?appcode=${process.env.VUE_APP_APPCODE}`
    })
}
export function getToken(){
    return request({
        url:`${process.env.VUE_APP_URL}/action/logic/getToken/anonymous?appcode=${process.env.VUE_APP_APPCODE}`
    })
}