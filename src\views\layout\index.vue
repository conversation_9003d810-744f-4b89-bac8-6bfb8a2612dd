<template>
    <div>
        <!-- <div v-if="$route.meta.title=='战团详情'||$route.meta.title=='详情'">
            <div class="nav">
                <span class="fl" style="padding-left:0.1rem;" @click="goBack()">
                    <van-icon name="arrow-left" />
                </span>
                详情
            </div>
            战团详情页面不缓存
            <keep-alive exclude="homeDetail,detail">
                <router-view style="padding:0.5rem 0;" />
            </keep-alive>
        </div> -->
        <div>
            
            <div class="nav" v-if="$route.meta.title!='干部作风'">
                {{$route.meta.title}}
                <img  v-if="$route.meta.show"  class="goBack" src="@/assets/images/goback2.png" alt="" @click="goBack()">
            </div>
            <keep-alive>
                <router-view :style="$route.meta.title=='干部作风'?'padding:0.2rem 0 0.5rem 0;':'padding:0.5rem 0'" />
            </keep-alive>
        </div>
    </div>
</template>
<script>
export default {
    name:'layout',
    data(){
        return{
           
        }
    },
    computed:{
    },
    methods:{
        goBack(){
            this.$router.back();
        },
    }
}
</script>
<style scoped>
/* 公共头部 */
.nav {
    width: 100%;
    height: 0.40rem;
    line-height: 0.40rem;
    position:fixed;
    top:0;
    z-index:10;
    background-color: #1989fa;
    color: #fff;
    font-size: 0.16rem;
    text-align: center;
}
.van-popup--right.van-popup--round{
    padding: 0.15rem 0;
}

.nav>span{
    display: inline-block;
    width: 0.8rem;
}

.goBack{
	  position: absolute;
	  right: .2rem;
	  top: .1rem;
	  width: .22rem;
  }

</style>