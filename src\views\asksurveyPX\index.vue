<template>
  <div class="maintext">
    <div class="textDec">
      <h3>{{ examName }}</h3>
      <!-- <p>
        您好！为进一步推动公司各级领导干部担当作为，现组织对分公司开展"担当作为"专项调查。
      </p>
      <p>
        本问卷不记名，严格保密，请您按照自己的真实看法填写即可。感谢您的支持和参与！
      </p> -->
      <p v-html="examRemark"></p>
    </div>

    <div class="subject" v-for="(item, i) in questionAll" :key="i">
      <!-- 题号和标题渲染 -->
      <!-- <div class="bigTit">
        <div v-show="i + 1 == 1">一、基本信息</div>
        <div v-show="i + 1 == 2">二、干部担当作为总体评价</div>
        <div v-show="i + 1 == 3">三、干部担当作为情况现状【第1题为单选，其他为多选，多选题最多选3项】</div>
      </div> -->
      <!-- {{item.questionType}} -->
      <!-- <div v-if="item.questionType === 'more'" class="question">
        <span v-if="i==2" > {{ i - 1 + "、" + "(单选)" + item.questionName }}</span>
        <span v-else > {{ i - 1 + "、" + "(多选)" + item.questionName }}</span>
      </div>
      <div v-else-if="item.questionType === 'shortAnswer'"   class="question">{{ 20 + "、" + item.questionName }}</div>
      <div v-else  class="question">{{ 1 + "、" + item.questionName }}</div> -->

      <div class="question" v-if="i == 0 && item.questionType === 'filling'" v-html=" item.questionName.replace('[[]]','').replace(/\n/g, '<br>')"></div>
      <div  class="question" v-if="item.questionType != 'filling'" >{{ item.questionName}}</div>
      <div class="question" v-if="item.questionType === 'filling' && i != 0 && item.questionClass != 'order' && item.questionClass != 'hk'">
        <span v-for="(part, partIndex) in parseQuestionText(item.questionName, i)" :key="partIndex">
          <span v-if="part.type === 'text'" v-html="part.content"></span>
          <input
            v-else-if="part.type === 'input'"
            type="text"
            class="filling inline-input"
            :value="getInputValue(i, part.inputIndex)"
            @input="handleInlineInput(i, part.inputIndex, $event)"
            @keypress="validateKeyPress(i, part.inputIndex, $event)"
            maxlength="1"
            style="text-transform: uppercase;"
          />
        </span>
      </div>
      <!-- 单选题 -->
      <van-radio-group v-if="item.questionType === 'filling'" v-model="result[i]"  :class="'styleMY'+i">
        <!-- 评分模式 -->
        <div >
          <div v-if="i == 0">
            <div class="slider-wrapper">
              <div class="slider-marks">
                <div class="mark-line" v-for="n in 21" :key="n" :class="{'mark-line-major': (n-1) % 2 === 0}"></div>
              </div>
              <van-slider 
                v-model="result[i]" 
                active-color="#c00000" 
                max="10" 
                min="0" 
                step="0.5"
                bar-height="4px" 
                class="custom-slider"
                :formatter="value => value.toFixed(1)"
              >
                <template #button>
                  <div class="custom-button">{{ result[i]?result[i].toFixed(1):'0.0' }}</div>
                </template>
              </van-slider>
              <div class="slider-numbers">
                <span class="number-label" v-for="n in 11" :key="n">{{ n - 1 }}</span>
              </div>
              <div class="slider-text-labels">
                <div>不满意</div>
                <div>非常满意</div>
              </div>
            </div>
          </div>
          <div v-else>
            <!-- <div v-for="(its, indexs) in item.answerList" :key="indexs" >
              <van-radio :name="its.answerCode" icon-size="18px"   >{{ its.answerCode + "、" + its.answerContent }}</van-radio>
            </div>
            <van-field autosize class="custom-field" v-if=" item.answerList[funZM(result[i])] ? item.answerList[funZM(result[i])].identification == '1' : false " 
            rows="3" type="textarea" v-model="zdytext[i]" placeholder="请填写" >
            </van-field> -->
        </div>

        </div>
        
      </van-radio-group>
      <!-- 多选题 -->
      <van-checkbox-group v-if="item.questionType === 'more'" v-model="result[i]" >
        <div v-for="(its, indexs) in item.answerList" :key="indexs">
          <van-checkbox
            shape="square"
            :name="its.answerCode"
            icon-size="18px"
            :disabled="its.disabled"
            @click="checkFun(item, its, i, indexs)"
            ref="checkboxes"
            >{{ its.answerCode + "、" + its.answerContent }}</van-checkbox
          >
        </div>
        <div v-for="it in result[i]" :key="it">
          <van-field
            autosize
            class="custom-field"
            v-if=" item.answerList[funZM(it)] ? item.answerList[funZM(it)]?.identification == '1' : false "
            rows="3"
            type="textarea"
            v-model="zdytext[i]"
            placeholder="请填写"
          ></van-field>
        </div>
      </van-checkbox-group>

      <!-- 简答题 -->
      <van-field
        v-if="item.questionType === 'shortAnswer'"
        autosize
        class="custom-field"
        v-model="result[i]"
        rows="3"
        type="textarea"
        placeholder="请填写"
      ></van-field>

      <!-- 排序题渲染 -->
      <template v-if="item.questionClass === 'order'">
        <div class="sort-title">{{ item.questionName }} <span style="color:#888;">【排序题】</span></div>
        
        <!-- 已选择项的拖拽排序区 -->
        <draggable
          v-if="item.sortedList && item.sortedList.length >= 2"
          v-model="item.sortedList"
          :options="{
            animation: 150,
            group: {
              name: 'sortedItems',
              pull: false,
              put: false
            },
            sort: true,
            disabled: false
          }"
          class="sorted-list"
          @start="handleDragStart(i)"
          @end="handleDragEnd(i)"
          @move="handleDragMove"
        >
          <div
            v-for="(opt, idx) in item.sortedList"
            :key="`sorted-${i}-${opt.key}-${idx}`"
            class="sorted-item"
          >
            <div class="sorted-item-content">
              <span
                class="sort-index mobile-cancel-btn"
                @click="cancelSelect(i, opt.key)"
                @touchstart="handleTouchStart"
                @touchend="handleTouchEnd(i, opt.key, $event)"
                @mousedown="handleMouseDown"
                @mouseup="handleMouseUp(i, opt.key, $event)"
                :data-question-index="i"
                :data-option-key="opt.key"
              >{{ idx+1 }}</span>
              <span
                class="sorted-item-text"
                @click="cancelSelect(i, opt.key)"
                @touchstart="handleTouchStart"
                @touchend="handleTouchEnd(i, opt.key, $event)"
              >{{ opt.key + "、" + opt.content }}</span>
              <i class="drag-icon" title="拖拽排序">
                <van-icon size="18" name="sort" />
              </i>
            </div>
          </div>
        </draggable>
        <div v-else-if="item.sortedList && item.sortedList.length === 1">
          <div
            v-for="(opt, idx) in item.sortedList"
            :key="`sorted-${i}-${opt.key}-${idx}`"
            class="sorted-item"
          >
            <div class="sorted-item-content">
              <span
                class="sort-index mobile-cancel-btn"
                @click="cancelSelect(i, opt.key)"
                @touchstart="handleTouchStart"
                @touchend="handleTouchEnd(i, opt.key, $event)"
                @mousedown="handleMouseDown"
                @mouseup="handleMouseUp(i, opt.key, $event)"
                :data-question-index="i"
                :data-option-key="opt.key"
              >{{ idx+1 }}</span>
              <span
                class="sorted-item-text"
                @click="cancelSelect(i, opt.key)"
                @touchstart="handleTouchStart"
                @touchend="handleTouchEnd(i, opt.key, $event)"
              >{{ opt.key + "、" + opt.content }}</span>
            </div>
          </div>
        </div>
        
        <!-- 未选择区 -->
        <van-checkbox-group
          v-model="item.checkboxValue"
          @change="handleSortCheckboxChange(i)"
        >
          <div class="sort-options-list">
            <div
              v-for="(opt, optIndex) in item.allOptions"
              :key="`unsorted-${i}-${opt.key}-${optIndex}`"
              v-show="!item.checkboxValue.includes(opt.key)"
              class="sort-option-wrapper"
              @click="toggleSortOption(i, opt.key)"
              @touchstart="handleTouchStart"
              @touchend="handleTouchEndForOption(i, opt.key, $event)"
            >
              <van-checkbox
                :name="opt.key"
                :disabled="!isOptionSelectable(i, opt)"
                shape="square"
                class="sort-checkbox-item"
                checked-color="#c00000"
                @click.stop="toggleSortOption(i, opt.key)"
              >
                <span class="sort-option-text">{{opt.key + "、" + opt.content }}</span>
              </van-checkbox>
            </div>
          </div>
        </van-checkbox-group>

        <!-- order为0的项永远显示在最后 -->
        <div v-if="item.orderZeroOptions && item.orderZeroOptions.length > 0" class="order-zero-section">
          <van-checkbox-group
            v-model="item.orderZeroCheckboxValue"
            @change="handleOrderZeroCheckboxChange(i)"
          >
            <div 
              v-for="(opt, idx) in item.orderZeroOptions" 
              :key="`order-zero-${i}-${opt.key}-${idx}`"
              class="order-zero-item"
            >
              <div class="order-zero-content">
                <van-checkbox
                  :name="opt.key"
                  :disabled="false"
                  shape="square"
                  class="order-zero-checkbox"
                  checked-color="#c00000"
                />
                <span class="order-zero-key">{{ opt.key }}、</span>
                <span v-if="!item.orderZeroInputs[opt.key] || item.orderZeroInputs[opt.key] === '其他'" >{{ item.orderZeroInputs[opt.key] || '其他' }}</span>
                <span v-else class="order-zero-input-active" >{{ item.orderZeroInputs[opt.key] }}</span>
              </div>
              <van-field
                v-if="item.orderZeroCheckboxValue.includes(opt.key)"
                v-model="item.orderZeroInputs[opt.key]"
                type="textarea"
                autosize
                placeholder="请输入内容"
                class="order-zero-textarea"
                @blur="saveOrderZeroInput(i, opt.key)"
              />
            </div>
          </van-checkbox-group>
        </div>
      </template>
    </div>


    <div class="textDec">
       <p v-html="examRemarkFooter"></p>
    </div>



    <div style="margin: 16px">
      <van-button round class="btn1" block type="info" @click="submit">提交</van-button>
    </div>
  </div>
</template>
<script>
import store from "@/store";
import { Dialog } from "vant";
import { Notify,Toast } from "vant";
import {
  constructExamLayout,
  findExamInfo,
  submitExam,
  findEffectiveExamByExamCode,
} from "@/api/homes.js";
import draggable from 'vuedraggable';

export default {
  name: "asksurvey",
  components: {
    draggable
  },
  data() {
    return {
      examAppCode: 'wyqk_2025_01',
      examCode: 'wyqk_2025_01',
      singleQuestionList: [], //单选
      moreQuestionList: [], //多选
      shortAnswerQuestionList: [], //简答
      questionAll: {}, //全部试题
      zdytext: [], //自定义文本
      result: [], //提交结果
      time: 10000, //时间
      examAnswer: [],
      examRecord: [],
      stime: 0,
      id: "",
      IntX: "",
      truename: store.state.user.user.truename, //姓名
      examName: "", //名称
      examRemark: "", //简介
      examRemarkFooter:"",
      resultOrg: {}, //原始提交结果；

      duoxunList: [],
      myindex: "",

      // 触摸事件相关
      touchStartTime: 0,
      touchStarted: false,

      // 鼠标事件相关
      mouseStartTime: 0,
      mousePressed: false,

      // 拖拽状态管理
      dragStartState: null,

    };
  },
  mounted() {
    this.initIOSInputFix();
  },
  created() {
    this.getList();
  },
  activated() {
    this.gettime();
  },
  methods: {
    // 解析问题文本，将[[]]转换为输入框标记
    parseQuestionText(questionName, questionIndex) {
      const questionNumber = questionIndex + 1;
      // 将换行符\n替换为<br>标签
      const processedQuestionName = questionName.replace(/\n/g, '<br>');
      const fullText =  processedQuestionName;

      // 计算当前问题中有多少个[[]]
      const matches = fullText.match(/\[\[\]\]/g);
      const inputCount = matches ? matches.length : 0;

      // 初始化result数组中对应位置的数据结构
      if (this.result[questionIndex] === undefined) {
        this.$set(this.result, questionIndex, inputCount > 1 ? new Array(inputCount).fill('') : '');
      }

      // 分割文本并标记输入框位置
      const parts = [];
      let currentText = fullText;
      let inputIndex = 0;

      while (currentText.includes('[[]]')) {
        const beforeInput = currentText.substring(0, currentText.indexOf('[[]]'));
        if (beforeInput) {
          parts.push({ type: 'text', content: beforeInput });
        }
        parts.push({ type: 'input', inputIndex: inputIndex++ });
        currentText = currentText.substring(currentText.indexOf('[[]]') + 4);
      }

      // 添加剩余的文本
      if (currentText) {
        parts.push({ type: 'text', content: currentText });
      }

      return parts;
    },

    // 获取输入框的值
    getInputValue(questionIndex, inputIndex) {
      if (Array.isArray(this.result[questionIndex])) {
        return this.result[questionIndex][inputIndex] || '';
      } else {
        return inputIndex === 0 ? (this.result[questionIndex] || '') : '';
      }
    },

    // 根据maxChooseNum获取允许的选项
    getAllowedOptions(questionIndex) {
      const question = this.questionAll[questionIndex];
      const maxChooseNum = question ? question.maxChooseNum : 5;

      // 如果maxChooseNum为null或undefined，默认允许ABCDE
      const maxNum = maxChooseNum || 5;

      // 生成从A开始的字母数组
      const options = [];
      for (let i = 0; i < Math.min(maxNum, 5); i++) {
        options.push(String.fromCharCode(65 + i)); // 65是'A'的ASCII码
      }

      return options;
    },

    // 获取允许选项的提示文本
    getAllowedOptionsText(questionIndex) {
      const options = this.getAllowedOptions(questionIndex);
      if (options.length === 1) {
        return `只能输入${options[0]}`;
      } else if (options.length === 2) {
        return `只能输入${options.join('或')}`;
      } else {
        return `只能输入${options.slice(0, -1).join('、')}或${options[options.length - 1]}`;
      }
    },

    // 获取当前输入框可用的选项（排除已被其他输入框使用的选项）
    getAvailableOptions(questionIndex, currentInputIndex) {
      const allOptions = this.getAllowedOptions(questionIndex);

      // 如果不是数组类型（单个输入框），返回所有选项
      if (!Array.isArray(this.result[questionIndex])) {
        return allOptions;
      }

      // 获取当前题目中其他输入框已使用的选项
      const usedOptions = [];
      const currentAnswers = this.result[questionIndex];
      for (let i = 0; i < currentAnswers.length; i++) {
        if (i !== currentInputIndex && currentAnswers[i]) {
          usedOptions.push(currentAnswers[i]);
        }
      }

      // 返回未被使用的选项
      return allOptions.filter(option => !usedOptions.includes(option));
    },

    // 获取输入框的占位符文本（考虑互斥性）
    getPlaceholderText(questionIndex, inputIndex = 0) {
      const availableOptions = this.getAvailableOptions(questionIndex, inputIndex);

      if (availableOptions.length === 0) {
        return '无可选项';
      } else if (availableOptions.length === 1) {
        return availableOptions[0];
      } else if (availableOptions.length <= 3) {
        return availableOptions.join('');
      } else {
        return availableOptions.join('');
      }
    },

    // 处理内联输入框的输入事件
    handleInlineInput(questionIndex, inputIndex, event) {
      const value = event.target.value.toUpperCase(); // 转换为大写

      // 获取当前题目允许的选项
      const allowedOptions = this.getAllowedOptions(questionIndex);

      // 验证输入值是否在允许的选项中
      if (value && !allowedOptions.includes(value)) {
        // 输入无效值时提示并清空
        const optionsText = this.getAllowedOptionsText(questionIndex);
        Toast({
          type: 'html',
          message: `<div>${optionsText}！</div>`
        });
        // 清空输入框
        this.clearInputValue(questionIndex, inputIndex, event);
        return;
      }

      // 检查互斥性：同一题目中其他输入框是否已经使用了这个选项
      if (value && Array.isArray(this.result[questionIndex])) {
        const currentAnswers = this.result[questionIndex];
        for (let i = 0; i < currentAnswers.length; i++) {
          if (i !== inputIndex && currentAnswers[i] === value) {
            // 发现重复选项，提示并清空
            Toast({
              type: 'html',
              message: `<div>选项${value}已被使用，请选择其他选项！</div>`
            });
            

            this.clearInputValue(questionIndex, inputIndex, event);
            return;
          }
        }
      }

      // 设置值
      if (Array.isArray(this.result[questionIndex])) {
        // 多个输入框的情况
        this.$set(this.result[questionIndex], inputIndex, value);
      } else {
        // 单个输入框的情况
        this.$set(this.result, questionIndex, value);
      }

      // 重新初始化iOS修复（因为DOM可能已更新）
      this.$nextTick(() => {
        this.updateIOSInputFix();
      });
    },

    // 清空输入框值的辅助方法
    clearInputValue(questionIndex, inputIndex, event) {
      if (Array.isArray(this.result[questionIndex])) {
        this.$set(this.result[questionIndex], inputIndex, '');
      } else {
        this.$set(this.result, questionIndex, '');
      }

      // 清空输入框显示
      event.target.value = '';
    },

    // 验证按键输入，根据maxChooseNum动态限制并考虑互斥性
    validateKeyPress(questionIndex, inputIndex, event) {
      const char = String.fromCharCode(event.keyCode || event.which).toUpperCase();

      // 获取当前输入框可用的选项（考虑互斥性）
      const availableOptions = this.getAvailableOptions(questionIndex, inputIndex);

      // 只允许当前输入框可用的字母
      if (!availableOptions.includes(char)) {
        event.preventDefault();

        // 如果不是控制键（如退格、删除等），显示提示
        if (event.keyCode > 31) {
          const allOptions = this.getAllowedOptions(questionIndex);

          if (allOptions.includes(char)) {
            // 选项在总的允许范围内，但已被其他输入框使用
             Toast({
              type: 'html',
              message: `<div>选项${char}已被使用，请选择其他选项！</div>`
            });
          } else {
            // 选项不在允许范围内
            const optionsText = this.getAllowedOptionsText(questionIndex);
             Toast({
              type: 'html',
              message: `<div>${optionsText}！</div>`
            });
            
          }
        }
      }
    },

    // 预览最终提交格式的方法
    previewSubmitFormat() {
      const preview = [];
      for (let i = 0; i < this.result.length; i++) {
        const answer = this.result[i];
        if (Array.isArray(answer)) {
          // 填空题多个答案用##分隔
          preview[i] = answer.join('##');
        } else {
          preview[i] = answer;
        }
      }
      return preview;
    },

    // iOS输入框优化初始化
    initIOSInputFix() {
      // 检测是否为iOS设备
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;

      if (isIOS) {
        // 防止iOS Safari在输入时的页面跳动
        this.$nextTick(() => {
          const inputs = document.querySelectorAll('.inline-input');

          inputs.forEach(input => {
            // 输入框聚焦时
            input.addEventListener('focus', (e) => {
              // 延迟执行，确保键盘弹出后再调整
              setTimeout(() => {
                // 滚动到输入框位置，但不要过度滚动
                const rect = e.target.getBoundingClientRect();
                const viewportHeight = window.innerHeight;

                // 如果输入框被键盘遮挡，轻微调整滚动位置
                if (rect.bottom > viewportHeight * 0.6) {
                  window.scrollBy(0, rect.bottom - viewportHeight * 0.6);
                }
              }, 300);
            });

            // 输入框失焦时
            input.addEventListener('blur', () => {
              // 延迟执行，确保键盘收起后再调整
              setTimeout(() => {
                // 重置页面缩放（如果有的话）
                document.querySelector('meta[name=viewport]').setAttribute(
                  'content',
                  'width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no,viewport-fit=cover'
                );
              }, 300);
            });
          });
        });
      }
    },

    // 更新iOS输入框修复（用于动态生成的输入框）
    updateIOSInputFix() {
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;

      if (isIOS) {
        const inputs = document.querySelectorAll('.inline-input:not([data-ios-fixed])');

        inputs.forEach(input => {
          // 标记已处理，避免重复绑定
          input.setAttribute('data-ios-fixed', 'true');

          // 输入框聚焦时
          input.addEventListener('focus', (e) => {
            setTimeout(() => {
              const rect = e.target.getBoundingClientRect();
              const viewportHeight = window.innerHeight;

              if (rect.bottom > viewportHeight * 0.6) {
                window.scrollBy(0, rect.bottom - viewportHeight * 0.6);
              }
            }, 300);
          });

          // 输入框失焦时
          input.addEventListener('blur', () => {
            setTimeout(() => {
              document.querySelector('meta[name=viewport]').setAttribute(
                'content',
                'width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no,viewport-fit=cover'
              );
            }, 300);
          });
        });
      }
    },

    // 处理触摸开始事件
    handleTouchStart(event) {
      // 记录触摸开始时间，用于区分点击和长按
      this.touchStartTime = Date.now();
      this.touchStarted = true;

      // 防止事件冒泡
      event.stopPropagation();
    },

    // 处理触摸结束事件
    handleTouchEnd(qIdx, key, event) {
      // 防止事件冒泡和默认行为
      event.preventDefault();
      event.stopPropagation();

      // 检查是否是有效的触摸（不是长按或滑动）
      if (this.touchStarted && Date.now() - this.touchStartTime < 500) {
        // 延迟执行，确保触摸事件完全结束
        setTimeout(() => {
          this.cancelSelect(qIdx, key);
        }, 50);
      }

      this.touchStarted = false;
    },

    // 处理鼠标按下事件（PC端备用）
    handleMouseDown(event) {
      this.mouseStartTime = Date.now();
      this.mousePressed = true;
      event.stopPropagation();
    },

    // 处理鼠标释放事件（PC端备用）
    handleMouseUp(qIdx, key, event) {
      event.preventDefault();
      event.stopPropagation();

      if (this.mousePressed && Date.now() - this.mouseStartTime < 500) {
        setTimeout(() => {
          this.cancelSelect(qIdx, key);
        }, 10);
      }

      this.mousePressed = false;
    },

    // 处理选项的触摸结束事件
    handleTouchEndForOption(qIdx, key, event) {
      // 防止事件冒泡和默认行为
      event.preventDefault();
      event.stopPropagation();

      // 检查是否是有效的触摸（不是长按或滑动）
      if (this.touchStarted && Date.now() - this.touchStartTime < 500) {
        // 延迟执行，确保触摸事件完全结束
        setTimeout(() => {
          this.toggleSortOption(qIdx, key);
        }, 50);
      }

      this.touchStarted = false;
    },

    // 切换排序选项的选中状态
    toggleSortOption(qIdx, key) {
      const sortObj = this.questionAll[qIdx];
      if (!sortObj || sortObj.questionClass !== 'order') {
        return;
      }

      // 检查选项是否可选
      const option = sortObj.allOptions.find(opt => opt.key === key);
      if (!option || !this.isOptionSelectable(qIdx, option)) {
        return;
      }

      // 检查是否已选中
      const isSelected = sortObj.checkboxValue.includes(key);

      if (isSelected) {
        // 如果已选中，则取消选中
        this.cancelSelect(qIdx, key);
      } else {
        // 如果未选中，则选中
        this.selectSortOption(qIdx, key);
      }
    },

    // 选中排序选项
    selectSortOption(qIdx, key) {
      const sortObj = this.questionAll[qIdx];
      if (!sortObj || sortObj.questionClass !== 'order') {
        return;
      }

      // 添加到checkboxValue
      const newCheckboxValue = [...sortObj.checkboxValue, key];
      this.$set(sortObj, 'checkboxValue', newCheckboxValue);

      // 更新sortedList
      const newSortedList = newCheckboxValue.map(key =>
        sortObj.allOptions.find(opt => opt.key === key)
      ).filter(Boolean);
      this.$set(sortObj, 'sortedList', newSortedList);

      // 强制更新视图
      this.$forceUpdate();

      // 显示选中成功的提示
      
    },

    // 多选单机事件
    checkFun(item, its, i, indexs) {
      if (its.answerContent == "不存在") { //如果选项是不存在控制选项是否可编辑
        if (this.result[i].includes(its.answerCode)) {
          this.result[i] = [its.answerCode];
          for (let i in item.answerList) {
            if (item.answerList[i].answerContent !== "不存在") {
              item.answerList[i].disabled = true;
            }
          }

          this.zdytext[i] = null
          this.$forceUpdate();
        } else {
          for (let i in item.answerList) {
            if (item.answerList[i].answerContent !== "不存在") {
              item.answerList[i].disabled = false;
            }
          }
          this.$forceUpdate();
        }
      }
      // 判断一下选择的选项中是否有其他选项
      let ident = ''
      for(let i in item.answerList){
        if(item.answerList[i].identification){
          ident = item.answerList[i].answerCode
        }
      }
      // if (this.result[i]?.length > item.maxChooseNum) {
      if (this.result[i]?.filter(item => item !== ident).length > item.maxChooseNum) {
        for (let i in this.duoxunList) {
          if (this.duoxunList[i].id == its.id) {
            this.myindex = i;
          }
        }
        this.$refs.checkboxes[this.myindex].toggle();
        this.result[i] = this.result[i].slice(0, this.result[i].length - 1);
        return Notify({ type: "warning", message: "除其他选项外,最多选择" + item.maxChooseNum + "项", });
      } else {
        this.result = this.sortNestedArrays(this.result) //把字母排序一下确保PC渲染无问题
      }
    },
    funZM(str) {//字母转换
      if (str) {
        return JSON.stringify(str).charCodeAt(1) - 65;
      }
    },
    // 字母排序
    sortNestedArrays(array) {
      return array.map(subArray => {
        if (Array.isArray(subArray)) {
            return subArray.sort((a, b) => a.localeCompare(b));
        }else{
          return subArray
        }

      });
    },
    getList() {
      let data = { examAppCode: this.examAppCode };
      constructExamLayout(data).then((res) => {
        this.examName = res.data.examName;
         this.examRemark = res.data.examRemark
         this.examRemarkFooter = res.data.examRemarkFooter
        this.singleQuestionList = res.data.fillingQuestionList; //单选
        this.moreQuestionList = res.data.moreQuestionList; //多选
        this.shortAnswerQuestionList = res.data.shortAnswerQuestionList; //简答
         
        // 处理排序题数据，直接添加到singleQuestionList中
        this.singleQuestionList.forEach((q) => {
          let titleDescription = q.titleDescription;
          if (typeof titleDescription === 'string' && titleDescription.trim() !== '') {
            try {
              titleDescription = JSON.parse(titleDescription);
            } catch (e) {
              titleDescription = [];
            }
          }
          
          if (q.questionType === 'filling' && q.questionClass == 'order' && Array.isArray(titleDescription)) {
            // 分离order为0的项和其他项
            const orderZeroOptions = titleDescription.filter(opt => opt.order === '0' || opt.order === 0);
            const normalOptions = titleDescription.filter(opt => opt.order !== '0' && opt.order !== 0);
            
            // 初始化orderZeroInputs，默认值为"其他"
            const orderZeroInputs = {};
            orderZeroOptions.forEach(opt => {
              orderZeroInputs[opt.key] = '其他';
            });
            
            // 使用Vue的响应式方法设置排序题数据
            this.$set(q, 'sortedList', []);
            this.$set(q, 'allOptions', normalOptions.map(opt => ({ ...opt })));
            this.$set(q, 'orderZeroOptions', orderZeroOptions.map(opt => ({ ...opt })));
            this.$set(q, 'orderZeroInputs', orderZeroInputs);
            this.$set(q, 'orderZeroCheckboxValue', []);
            this.$set(q, 'checkboxValue', []);
          }
        });
         
        this.questionAll = this.singleQuestionList.concat(
          this.moreQuestionList,
          this.shortAnswerQuestionList
        );
        this.questionAll.sort((a, b) => a.questionOrder - b.questionOrder);
        this.duoxunList = this.moreQuestionList
          .map((a) => {
            return a.answerList.map((b) => b);
          })
          .flat();

        let Record = "";
        for (var i = 0; i < this.questionAll.length; i++) {
          Record += this.questionAll[i].questionCode + ",";
        }
        this.examRecord = Record.substring(0, Record.length - 1);
      });
      console.log(this.questionAll);
      
    },
    gettime() {
      //获取考试信息
      // 判断是否可以开始答题
      findEffectiveExamByExamCode({ examCodes: this.examCode }).then((res) => {
          if (res.data.showFlag === true) {
            let data = {
              examAppCode: this.examAppCode,
              examCode: this.examCode,
              publishUsername: store.state.user.user.username,
            };
            findExamInfo(data).then((res) => {
              if (res.data) {
                if (!res.data.isFinishExam && !res.data.isMarkingExam) {
                  this.getList();
                } else {
                  this.$router.push({ name: "success" });
                }
              } else {
                this.getList();
              }
            });
          } else {
            Dialog.alert({
              title: "",
              message: "竞赛不在考试时间范围或者暂未权限！",
            }).then(() => {
              window.close();
              window.open("about:blank", "_self");
            });
          }
        }).catch(() => {
          Dialog.alert({
            title: "",
            message: "竞赛不在考试时间范围或者暂未权限！",
          }).then(() => {
            window.close();
            window.open("about:blank", "_self");
          });
        });
    },
    submit() {
      // 验证所有题目是否完成
      let arr = [];
      for(let i in this.result){
        const answer = this.result[i];
        let isCompleted = false;

        if (Array.isArray(answer)) {
          // 数组类型（多个输入框）- 检查是否所有输入框都有值
          isCompleted = answer.length > 0 && answer.every(item => item && item.toString().trim().length > 0);
        } else {
          // 字符串或数字类型（单个输入框或滑块）
          isCompleted = answer !== undefined && answer !== null && answer.toString().trim().length > 0;
        }

        if (isCompleted) {
          arr.push(answer);
        }
      }

      // 检查排序题是否完成
      for(let i = 0; i < this.questionAll.length; i++) {
        const question = this.questionAll[i];
        if (question.questionClass === 'order') {
          // 检查排序题是否有选择项
          const hasSortedItems = question.sortedList && question.sortedList.length > 0;
          const hasOrderZeroItems = question.orderZeroOptions && question.orderZeroOptions.length > 0;
          
          if (hasSortedItems || hasOrderZeroItems) {
            arr.push('completed'); // 添加一个标记表示排序题已完成
          }
        }
      }

      if (arr.length !== this.questionAll.length) {
        return Toast({
              type: 'html',
              message: `<div>您有未完成的题目，请继续填写！</div>`
            });
      }

      for (var i in this.result) {
        if (typeof this.result[i] == "string") {
          if (this.zdytext[i]) {
          } else {
            if (
              this.questionAll[i].answerList[this.funZM(this.result[i])] &&
              this.questionAll[i].answerList[this.funZM(this.result[i])]
                ?.identification == "1"
            ) {
               return Toast({
                  type: 'html',
                  message: `<div>您有未完成的题目，请填写具体说明！</div>`
                });
              
            }
          }
        } else if (typeof this.result[i] == "object") {
          if (this.zdytext[i]) {
          } else {
            for (var v in this.result[i]) {
              if (
                this.questionAll[i].answerList[this.funZM(this.result[i][v])]
                  ?.identification == "1"
              ) {
                 return Toast({
                  type: 'html',
                  message: `<div>您有未完成的题目，请填写具体说明！</div>`
                });
                
              }
            }
          }
        }
      }
      Dialog.confirm({
        title: "温馨提示",
        message: "您已完成了所有题目，请确认是否进行提交",
        confirmButtonColor: "#c00000",
      }).then(() => {
          this.resultOrg = this.deepCopy(this.result);
          console.log(this.resultOrg);
          
          // 处理排序题数据，添加到resultOrg中
          for (let i = 0; i < this.questionAll.length; i++) {
            const question = this.questionAll[i];
            if (question.questionClass === 'order') {
              let sortResult = '';
              
              // 处理正常排序的项
              if (question.sortedList && question.sortedList.length > 0) {
                sortResult = question.sortedList.map(opt => opt.key).join('');
              }
              
              // 处理order为0的项
              if (question.orderZeroOptions && question.orderZeroOptions.length > 0) {
                const orderZeroResults = question.orderZeroCheckboxValue.map(key => {
                  const opt = question.orderZeroOptions.find(o => o.key === key);
                  const inputContent = question.orderZeroInputs[opt.key] || opt.content;
                  return `${opt.key}:${inputContent}`;
                });
                
                if (sortResult && orderZeroResults.length > 0) {
                  sortResult += orderZeroResults.join('、');
                } else if (orderZeroResults.length > 0) {
                  sortResult = orderZeroResults.join('、');
                }
              }
              
              // 将排序题结果添加到resultOrg中
              this.$set(this.resultOrg, i, sortResult);
            }
          }
          console.log(this.resultOrg);
          
          for (var i in this.resultOrg) {
            if (typeof this.resultOrg[i] == "string") {
              if (this.zdytext[i]) {
                if (
                  this.questionAll[i].answerList[this.funZM(this.resultOrg[i])] &&
                  this.questionAll[i].answerList[this.funZM(this.resultOrg[i])]?.identification == "1"
                ) {
                  this.resultOrg[i] = this.resultOrg[i] + ":" + this.zdytext[i];
                }
              }
            } else if (typeof this.resultOrg[i] == "object") {
              if (this.zdytext[i]) {
                let index = this.resultOrg[i].length - 1;
                this.resultOrg[i][index] =
                  this.resultOrg[i][index] + ":" + this.zdytext[i];
              }
            }else if (typeof this.resultOrg[i] == "number") {
              this.resultOrg[i] = this.resultOrg[i].toString()
            }
          }

          let aboutResult = [];
          for (var j in this.resultOrg) {
            if (typeof this.resultOrg[j] !== "string") {
              // 判断是否为填空题
              if (this.questionAll[j] && this.questionAll[j].questionType === 'filling') {
                // 填空题的多个答案用##分隔
                aboutResult[j] = this.resultOrg[j].join("##");
              } else {
                // 其他类型题目（如多选题）用/分隔
                aboutResult[j] = this.resultOrg[j].join("/");
              }
            } else {
              aboutResult[j] = this.resultOrg[j];
            }
          }

          let data = {
            examAppCode: this.examAppCode,
            examCode: this.examCode,
            publishUsername: store.state.user.user.username,
            residueTime: this.stime,
            examAnswer: aboutResult.toString(),
            examRecord: this.examRecord,
          };

          let arrs = [];
          for (let u in this.questionAll) {
            if (this.questionAll[u].questionType == "more") {
              arrs.push({
                questionCode: this.questionAll[u].questionCode,
                maxChooseNum: this.questionAll[u].maxChooseNum,
                examAnswer: aboutResult[u],
              });
            } else {
              arrs.push({
                questionCode: this.questionAll[u].questionCode,
                examAnswer: aboutResult[u],
              });
            }
          }
          data.examInfoList = arrs;

          // 详细的调试日志
          console.log('=== 填空题提交数据处理 ===');
          console.log('原始result数据:', this.result);
          console.log('处理后的resultOrg数据:', this.resultOrg);
          console.log('最终aboutResult数据:', aboutResult);
          console.log('完整提交数据:', data);
          console.log('=== 处理完成 ===');

          // return false
          submitExam(data).then((res) => {
            if (res.status == 200) {
              clearInterval(this.IntX);
              this.result = [];
              this.zdytext = [];
              this.djsdiv = false;
              this.$router.push({ name: "success" });
            }
          });
        })
        .catch(() => {});
    },

    // 使用Vue提供的工具函数deepCopy进行深拷贝
    deepCopy(obj) {
      return JSON.parse(JSON.stringify(obj));
    },

    // 获取排序题对象
    getSortObjByIndex(idx) {
      return this.questionAll[idx] || {
        sortedList: [],
        allOptions: [],
        orderZeroOptions: [],
        orderZeroInputs: {},
        orderZeroCheckboxValue: [],
        checkboxValue: []
      };
    },
    // 判断选项是否可选
    isOptionSelectable(qIdx, opt) {
      // 正常的排序选项应该是order不为0的项
      return opt.order !== '0' && opt.order !== 0;
    },
    // 处理checkbox变化
    handleSortCheckboxChange(qIdx) {
      const sortObj = this.questionAll[qIdx];
      if (sortObj && sortObj.questionClass === 'order') {
        // 根据checkboxValue的顺序更新sortedList
        const newSortedList = sortObj.checkboxValue.map(key =>
          sortObj.allOptions.find(opt => opt.key === key)
        ).filter(Boolean);
        this.$set(sortObj, 'sortedList', newSortedList);
      }
    },
    // 拖拽开始事件
    handleDragStart(qIdx) {
      const sortObj = this.questionAll[qIdx];
      if (sortObj && sortObj.questionClass === 'order') {
        // 记录拖拽开始时的状态，用于防止意外修改
        this.dragStartState = {
          qIdx: qIdx,
          sortedList: [...sortObj.sortedList],
          checkboxValue: [...sortObj.checkboxValue]
        };
      }
    },

    // 拖拽移动事件 - 限制拖拽边界
    handleDragMove(evt) {
      // 确保拖拽只在当前排序区域内进行
      const { from, to } = evt;

      // 如果目标容器不是排序列表，则阻止拖拽
      if (!to.classList.contains('sorted-list')) {
        return false;
      }

      // 如果来源容器不是排序列表，则阻止拖拽
      if (!from.classList.contains('sorted-list')) {
        return false;
      }

      return true;
    },

    handleDragEnd(qIdx) {
      const sortObj = this.questionAll[qIdx];
      if (sortObj && sortObj.questionClass === 'order') {
        try {
          // 验证拖拽后的数据完整性
          const currentKeys = sortObj.sortedList.map(opt => opt.key);
          const originalKeys = this.dragStartState ? this.dragStartState.checkboxValue : [];

          // 检查是否有重复或丢失的元素
          const hasValidData = currentKeys.length === originalKeys.length &&
                              currentKeys.every(key => originalKeys.includes(key)) &&
                              originalKeys.every(key => currentKeys.includes(key));

          if (hasValidData) {
            // 拖拽结束后，根据sortedList的新顺序更新checkboxValue
            const newCheckboxValue = sortObj.sortedList.map(opt => opt.key);
            this.$set(sortObj, 'checkboxValue', newCheckboxValue);

            // 强制更新视图以确保排序号正确显示
            this.$forceUpdate();

            console.log('拖拽排序完成:', newCheckboxValue);
          } else {
            // 如果数据不完整，恢复到拖拽前的状态
            console.warn('拖拽数据异常，恢复原状态');
            if (this.dragStartState) {
              this.$set(sortObj, 'sortedList', [...this.dragStartState.sortedList]);
              this.$set(sortObj, 'checkboxValue', [...this.dragStartState.checkboxValue]);
            }
          }
        } catch (error) {
          console.error('拖拽处理出错:', error);
          // 出错时恢复原状态
          if (this.dragStartState) {
            this.$set(sortObj, 'sortedList', [...this.dragStartState.sortedList]);
            this.$set(sortObj, 'checkboxValue', [...this.dragStartState.checkboxValue]);
          }
        } finally {
          // 清理拖拽状态
          this.dragStartState = null;
        }
      }
    },
    cancelSelect(qIdx, key) {
      console.log('cancelSelect called:', qIdx, key); // 调试日志

      const sortObj = this.questionAll[qIdx];
      if (sortObj && sortObj.questionClass === 'order') {
        console.log('Before cancel - checkboxValue:', sortObj.checkboxValue); // 调试日志

        // 从checkboxValue中移除该选项
        const newCheckboxValue = sortObj.checkboxValue.filter(k => k !== key);
        this.$set(sortObj, 'checkboxValue', newCheckboxValue);

        // 更新sortedList
        const newSortedList = newCheckboxValue.map(key =>
          sortObj.allOptions.find(opt => opt.key === key)
        ).filter(Boolean);
        this.$set(sortObj, 'sortedList', newSortedList);

        // 将取消选中的选项移动到allOptions的最前面
        const canceledOption = sortObj.allOptions.find(opt => opt.key === key);
        if (canceledOption) {
          // 从原位置移除
          const newAllOptions = sortObj.allOptions.filter(opt => opt.key !== key);
          // 添加到最前面
          newAllOptions.unshift(canceledOption);
          this.$set(sortObj, 'allOptions', newAllOptions);
        }

        console.log('After cancel - checkboxValue:', sortObj.checkboxValue); // 调试日志

        // 多重强制更新，确保移动端正确刷新
        this.$forceUpdate();

        // 使用nextTick确保DOM更新
        this.$nextTick(() => {
          this.$forceUpdate();

          // 再次延迟更新
          setTimeout(() => {
            this.$forceUpdate();
            console.log('Force update completed'); // 调试日志
          }, 100);
        });

        
      }
    },
    toggleOrderZeroInput(qIdx, key) {
      const sortObj = this.questionAll[qIdx];
      if (sortObj && sortObj.questionClass === 'order') {
        const opt = sortObj.orderZeroOptions.find(o => o.key === key);
        if (opt) {
          // 如果当前显示的是默认内容"其他"，则设置为空字符串以显示输入框
          if (!sortObj.orderZeroInputs[key] || sortObj.orderZeroInputs[key] === '其他') {
            this.$set(sortObj.orderZeroInputs, key, '');
          } else {
            // 如果已经修改过，则隐藏输入框，恢复默认值
            this.$set(sortObj.orderZeroInputs, key, '其他');
          }
        }
      }
    },
    saveOrderZeroInput(qIdx, key) {
      const sortObj = this.questionAll[qIdx];
      if (sortObj && sortObj.questionClass === 'order') {
        const opt = sortObj.orderZeroOptions.find(o => o.key === key);
        if (opt) {
          const trimmedValue = sortObj.orderZeroInputs[key].trim();
          // 如果输入为空或与默认内容相同，则恢复默认值
          if (!trimmedValue || trimmedValue === '其他') {
            this.$set(sortObj.orderZeroInputs, key, '其他');
          } else {
            this.$set(sortObj.orderZeroInputs, key, trimmedValue);
          }
        }
      }
    },
    handleOrderZeroCheckboxChange(qIdx) {
      const sortObj = this.questionAll[qIdx];
      if (sortObj && sortObj.questionClass === 'order') {
        // 当选中checkbox时，如果内容还是默认的"其他"，设置为空字符串以显示输入框
        sortObj.orderZeroCheckboxValue.forEach(key => {
          if (sortObj.orderZeroInputs[key] === '其他') {
            this.$set(sortObj.orderZeroInputs, key, '其他');
          }
        });
        
        // 当取消选中时，恢复默认值
        sortObj.orderZeroOptions.forEach(opt => {
          if (!sortObj.orderZeroCheckboxValue.includes(opt.key)) {
            this.$set(sortObj.orderZeroInputs, opt.key, '其他');
          }
        });
      }
    },
  },
};
</script>

<style scoped>
/* iOS输入框优化 - 防止页面缩放和跳动 */
html {
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

body {
  -webkit-overflow-scrolling: touch;
  overflow-x: hidden;
  position: relative;
}

/* 防止iOS Safari在输入时的页面跳动和缩放 */
input[type="text"],
input[type="number"],
textarea {
  -webkit-appearance: none;
  appearance: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-user-select: text;
  user-select: text;
  border-radius: 0;
  /* 防止输入时页面跳动 */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

/* 容器优化 */
.container {
  position: relative;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}

.maintext {
  margin-left: 0.3rem;
  margin-right: 0.3rem;
}
.van-radio {
  margin-bottom: 0.05rem;
  align-items: flex-start;
}
.subject {
  margin-bottom: 0.2rem;
}
.djs {
  position: fixed;
  top: 0.8rem;
  right: 0.3rem;
  background: #fff;
  display: flex;
  align-items: center;
  z-index: 2;
}
.question {
  line-height: 0.22rem;
  margin-bottom: 0.1rem;
  font-weight: 700;
}
.custom-field {
  border: 1px solid #e8e8e8;
  border-radius: 5px;
  padding: 5px;
}
.van-checkbox {
  align-items: flex-start;
  margin-bottom: 3px;
}

.textDec {
  color: #333;
  font-size: 14px;
  line-height: 22px;
  margin-bottom: 3px;
}
.textDec h3 {
  text-align: center;
  font-weight: bold;
  margin-bottom: 1px;
}
.textDec p {
  text-indent: 2em;
}

.bigTit div {
  font-size: 16px;
  margin-bottom: 5px;
  font-weight: 700;
}

.styleMY1>div{
  display: flex;
  flex-wrap: wrap;
}



.slider-wrapper {
  position: relative;
  width: 100%; /* Overall width */
  margin: 20px auto;
  padding-top: 25px; /* Adjusted space for marks above */
  padding-bottom: 40px; /* Space for numbers and labels below */
}

.slider-marks {
  position: absolute;
  top: 0; /* Align with top padding of wrapper */
  left: 16px; /* Adjusted to align precisely with van-slider track start */
  width: calc(100% - 20px); /* Adjusted to align precisely with van-slider track end */
  display: flex;
  justify-content: space-between;
  z-index: 1;
  pointer-events: none;
  align-items:flex-end;
}

.mark-line {
  width: 1px; /* Thinner lines */
  background-color: #ccc; /* Lighter color */
  height: 6px; /* Default height */
}
.mark-line-major {
  height: 10px; /* Taller for major marks */
  background-color: #999; /* Darker for major marks */
}

.custom-slider {
  position: absolute; /* Changed to absolute for precise positioning */
  z-index: 2; /* Ensure slider is interactive */
  margin-top: 0; /* Remove previous margin */
  top: 19px; /* Position the slider to align its track visually between marks and numbers */
  left: 16px; /* Align with marks and numbers */
  width: calc(100% - 20px); /* Align with marks and numbers */
}

.custom-button {
  width: 20px; /* Adjusted width */
  height: 20px; /* Adjusted height */
  line-height: 20px; /* Center text vertically */
  color: #fff;
  font-size: 10px; /* Adjusted font size */
  text-align: center;
  background-color: #c00000;
  border-radius: 30%; /* Perfect circle */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15); /* Slightly lighter shadow */
}

.slider-numbers {
  position: absolute;
  top: 31px; /* Position numbers below the slider and marks, adjusted for spacing */
  bottom: auto; /* Ensure bottom positioning is not used */
  left: 16px; /* Adjusted to align precisely with van-slider track start */
  width: calc(100% - 7px); /* Adjusted to align precisely with van-slider track end */
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666; /* Revert to original number color */
  z-index: 1;
  pointer-events: none;
}

.number-label {
  position: relative;
  transform: translateX(-50%); /* Center text under tick */
  white-space: nowrap;
}
.number-label:first-child {
  transform: translateX(0%); /* Align 0 to start */
}
.number-label:last-child {
  transform: translateX(-50%); /* Align 10 to end */
}

.slider-text-labels {
  position: absolute; /* Absolute position relative to wrapper */
  bottom: 0; /* Align with bottom padding */
  left: 16px; /* Align with track start */
  width: calc(100% - 15px); /* Align with track end */
  display: flex;
  justify-content: space-between;
  color: #666; /* Revert to original color */
  font-size: 12px; /* Revert to original font size */
  font-weight: normal; /* Remove bold */
}
.filling{
    border: none;
    border-bottom: 1px solid #000;
    padding: 0 5px;
    width: 80px;
}

.inline-input {
    padding: 2px 8px;
    width: 80px;
    margin: 0 4px;
    background: transparent;
    outline: none;
    font-size: 14px;
    color: #1989fa;
    text-align: center;
    font-weight: bold;
    text-transform: uppercase;
    /* iOS优化 */
    -webkit-appearance: none;
    appearance: none;
    -webkit-tap-highlight-color: transparent;
    -webkit-user-select: text;
    user-select: text;
    border-radius: 0;
    /* 防止iOS输入时页面跳动 */
    position: relative;
    z-index: 1;
}

.inline-input:focus {
    border-bottom: 2px solid #1989fa;
    background: rgba(25, 137, 250, 0.05);
    /* iOS聚焦时防止页面跳动 */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.inline-input::placeholder {
    color: #999;
    font-size: 12px;
    text-align: center;
}
.btn1{
  background-color: #c00000;
  border: 1px solid #c00000;
}
.sort-option.disabled {
  color: #ccc;
  cursor: not-allowed;
}
.sorted-list .drag-icon {
  font-size: 18px;
  color: #888;
}
.sorted-item {
  background: #f7f8fa;
  margin-bottom: 4px;
}
.sort-title {
  font-weight: bold;
  margin-bottom: 6px;
}
.sort-options-list {
  margin-bottom: 10px;
}
.sort-checkbox-item {
  display: block;
  padding: 8px;
  border-radius: 4px;
  cursor: move;
  display: flex;
}
.sort-checkbox-item:hover {
  background: #f0f0f0;
  border-color: #1989fa;
}
.sort-number {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: #1989fa;
  color: #fff;
  text-align: center;
  border-radius: 50%;
  margin-right: 8px;
  font-size: 12px;
  line-height: 20px;
  font-weight: bold;
}
.sorted-list {
  margin-top: 10px;
  border-radius: 4px;
}
.sorted-item {
  background: #fff;
  border-radius: 4px;
}
.sorted-item:last-child {
  margin-bottom: 0;
}
.drag-icon {
  font-size: 16px;
  color: #999;
  cursor: move;
}
/* 排序题样式优化 */
.sorted-item {
  margin: 8px 0;
}

.sorted-item:hover {
  border-color: #c00000;
  background: #fff5f5;
}

.sorted-item-content {
  display: flex;
  align-items: center;
  padding: 5px 7px;
}

.sort-index {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  background: #c00000;
  color: #fff;
  text-align: center;
  border-radius: 4px;
  margin-right: 12px;
  cursor: pointer;
  line-height: 24px;
  font-size: 14px;
  font-weight: bold;
  /* 移动端触摸优化 */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  touch-action: manipulation;
  /* 确保在移动端有足够的触摸区域 */
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.sort-index:hover {
  background: #ff4757 !important;
  transform: scale(1.05);
}

/* 移动端触摸状态 */
.sort-index:active {
  background: #ff4757 !important;
  transform: scale(0.95);
  transition: all 0.1s ease;
}

.sorted-item-text {
  flex: 1;
  font-size: 14px;
  line-height: 1.5;
  cursor: pointer;
  padding: 0px 0;
  -webkit-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.sorted-item-text:hover {
  color: #c00000;
}

.drag-icon {
  margin-left: auto;
  cursor: move;
  color: #999;
  -webkit-user-select: none;
  user-select: none;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.drag-icon:hover {
  color: #c00000;
  background: rgba(192, 0, 0, 0.1);
  transform: scale(1.1);
}

.drag-icon:active {
  transform: scale(0.95);
}

/* 未选择区样式优化 */
.sort-options-list {
  margin-top: 12px;
}

.sort-option-wrapper {
  background: #fff;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  -webkit-user-select: none;
  user-select: none;
}

.sort-option-wrapper:hover {
  border-color: #c00000;
  background: #fff5f5;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(192, 0, 0, 0.1);
}

.sort-option-wrapper:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(192, 0, 0, 0.1);
}

.sort-checkbox-item {
  width: 100%;
  padding: 5px 7px;
  margin: 0;
  display: flex;
  align-items: center;
}

.sort-option-text {
  font-size: 14px;
  line-height: 1.5;
  margin-left: 8px;
  flex: 1;
}

/* 确保复选框与排序框大小一致 */
.sort-checkbox-item .van-checkbox__icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
}

.sort-checkbox-item .van-checkbox__icon .van-icon {
  width: 24px;
  height: 24px;
  line-height: 24px;
}

/* 移动端取消按钮优化 */
.mobile-cancel-btn {
  /* 确保在移动端有良好的触摸体验 */
  position: relative;
  z-index: 10;
  /* 防止文本选择 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  /* 防止长按菜单 */
  -webkit-touch-callout: none;
  /* 移除默认的触摸高亮 */
  -webkit-tap-highlight-color: transparent;
  /* 快速响应触摸 */
  touch-action: manipulation;
}


  
.order-zero-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
  font-size: 14px;
}
.order-zero-item {
  background: #fff;
  margin-left: 8px;
}
.order-zero-content {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 4px 0;
}
.order-zero-content:hover {
  border-radius: 2px;
}
.order-zero-checkbox {
  margin-right: 8px;
}
.order-zero-key {
  color: #323233;
  line-height: 20px;
  margin-left: 8px;
}
.order-zero-input-active {
  color: #323233;
}
.order-zero-textarea {
  margin-top: 8px;
  border: 1px solid #999;
  border-radius: 4px;
}
</style>
