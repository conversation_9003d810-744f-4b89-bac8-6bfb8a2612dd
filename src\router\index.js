import Vue from 'vue'
import Router from 'vue-router'
import Layout from '@/views/layout';

Vue.use(Router)
export const constantRouterMap = [
    // login0、login1手动切换成login
    {
        path: '/login',
        name: 'login',
        component: () => import('@/views/login/Login'),
        meta: { title: '登录', icon: 'login' },
        hidden: true
    },
    {
        path: '/login0',
        name: 'login',
        component: () => import('@/views/login/Login0'),
        meta: { title: '登录测试入口', icon: 'login' },
        hidden: true
    },
    // 答题页面（倒计时、间隔报错、时间结束自动提交、结束自动计分、答题情况回显）
    // 中转页搭配使用
    {
        path: '',
        redirect: '/home',
        name: '首页',
        meta: { title: '首页' },
        component: Layout,
        children: [
            {
                meta: { title: '' },
                name: 'home',
                path: '/home',
                component: () => import('@/views/home')
            },
        ]
    },
    //提交成功页面
    {
        path: '/success',
        name: 'success',
        component: () => import('@/views/success'),
        meta: { title: '提示', },
        hidden: true
    },

    //asksurvey通用调查问卷,asksurveyMzcp领导班子及领导人员民主测评表(问卷)
    {
        path: '/askcheck',
        meta: { title: '' },
        redirect: '/asksurvey',
        name: 'askcheck',
        component: Layout,
        children: [
            {
                meta: { title: '调查问卷' },
                name: 'asksurvey',
                path: '/asksurvey',
                component: () => import('@/views/asksurvey')
            },
            {
                meta: { title: '领导班子及领导人员民主测评表' },
                name: 'asksurveyMzcp',
                path: '/asksurveyMzcp',
                component: () => import('@/views/asksurveyMzcp')
            },
            // {
            //     path: '/asksurveyJjdc',
            //     name: 'asksurveyJjdc',
            //     component: () => import ('@/views/asksurveyJjdc'),
            //     meta: { title: '测评问卷', },
            //     hidden: true
            // },  
            {
                path: '/asksurveyJdydt',
                name: 'asksurveyJdydt',
                component: () => import('@/views/asksurveyJdydt'),
                meta: { title: '测评问卷', },
                hidden: true
            },
            {
                path: '/asksurveyDdzw',
                name: 'asksurveyDdzw',
                component: () => import('@/views/asksurveyDdzw'),
                meta: { title: '调查问卷', },
                hidden: true
            },
            {
                path: '/asksurveyCYZD',
                name: 'asksurveyCYZD',
                component: () => import('@/views/asksurveyCYZD'),
                meta: { title: '调查问卷', },
                hidden: true
            },
            {
                path: '/asksurveyNbxc',
                name: 'asksurveyNbxc',
                component: () => import('@/views/asksurveyNbxc'),
                meta: { title: '2024年公司党委第二轮内部巡察调查问卷', },
                hidden: true
            },
            {
                path: '/asksurveyXcb',
                name: 'asksurveyXcb',
                component: () => import('@/views/asksurveyXcb'),
                meta: { title: '调查问卷', },
                hidden: true
            },
            {
                path: '/asksurveyJJBM',
                name: 'asksurveyJJBM',
                component: () => import('@/views/asksurveyJJBM'),
                meta: { title: '干部作风', },
                hidden: true
            },
            {
                path: '/asksurveyPX',
                name: 'asksurveyPX',
                component: () => import('@/views/asksurveyPX'),
                meta: { title: '干部作风', },
                hidden: true
            },
        ]
    },
    {
        path: '/asksurveyDict',
        name: 'asksurveyDict',
        component: () => import('@/views/asksurveyDict'),
        meta: { title: 'DICT项目支撑评分', },
        hidden: true
    },
    // 合规知识竞赛
    // {
    //     path: '/knowContest/home',
    //     meta:{title:'首页'},
    //     name: 'home',
    //     component: () => import('@/views/knowContest/home')
    // },
    // {
    //     path: '/knowContest/test',
    //     meta:{title:'答题'},
    //     name: 'test',
    //     component: () => import('@/views/knowContest/test')
    // },
    // {
    //     path: '/knowContest/select',
    //     meta:{title:'选择对战人'},
    //     name: 'select',
    //     component: () => import('@/views/knowContest/select')
    // },
    // {
    //     path: '/getOrgAndUser',
    //     name: 'getOrgAndUser',
    //     meta:{title:'选择人'},
    //     component: () =>import ('@/components/GetOrgAndUser'),
    // },
    // {
    //     path: '/knowContest/rank',
    //     name: 'rank',
    //     meta:{title:'排行榜'},
    //     component: () =>import ('@/views/knowContest/rank'),
    // },


    //附件区
    // {
    //     path: '/download',
    //     name: 'download',
    //     component: () => import ('@/views/download'),
    //     meta: { title: '', },
    //     hidden: true
    // },
    //获奖填写页面
    // {
    //     path: '/info',
    //     meta:{title:''},
    //     redirect: '/info',
    //     name: 'info',
    //     component: Layout,
    //     children: [
    //         {
    //             meta:{title:'填写信息'},
    //             name: 'info',
    //             path: '/info',
    //             component: () => import('@/views/info')
    //         },
    //     ]
    // },
    // 2023年明纪守法知识竞赛待办中转
    // {
    //     path: '/detail',
    //     meta:{title:''},
    //     name: 'detail',
    //     component: () => import('@/views/detail')
    // },
    // 纪检初级岗位能力测评考试（补考）待办中转
    // {
    //     path: '/todolist',
    //     meta:{title:''},
    //     name: 'todolist',
    //     component: () => import('@/views/todolist')
    // },
    // {
    //     path: '/home/<USER>',
    //     meta:{title:''},
    //     name: 'indexWlqg',
    //     component: () => import('@/views/home/<USER>')
    // },

];

export default new Router({
    routes: constantRouterMap,
    scrollBehavior(to, from, savedPosition) {
        if (savedPosition) {
            return savedPosition
        } else {
            return {
                x: 0,
                y: 0
            }
        }
    }
})